Yapp/src/main/java/com/example/abonekaptanmobile/data/repository/SubscriptionRepository.kt^app/src/main/java/com/example/abonekaptanmobile/data/local/entity/SubscriptionPatternEntity.ktPapp/src/main/java/com/example/abonekaptanmobile/workers/ProcessFeedbackWorker.ktXapp/src/main/java/com/example/abonekaptanmobile/data/repository/HuggingFaceRepository.ktUapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/CommunityPatternDao.ktAapp/src/main/java/com/example/abonekaptanmobile/AboneKaptanApp.ktVapp/src/main/java/com/example/abonekaptanmobile/ui/viewmodel/StageAnalysisViewModel.ktRapp/src/main/java/com/example/abonekaptanmobile/data/repository/GmailRepository.ktMapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/FeedbackDao.ktJapp/src/main/java/com/example/abonekaptanmobile/ui/screens/SignInScreen.ktIapp/src/main/java/com/example/abonekaptanmobile/model/CancellationInfo.ktVapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/HuggingFaceModels.ktOapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/GroqModels.ktWapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/SubscriptionEntity.ktZapp/src/main/java/com/example/abonekaptanmobile/data/repository/StageAnalysisRepository.ktMapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/GmailApi.ktQapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/SubscriptionDao.kt?app/src/main/java/com/example/abonekaptanmobile/MainActivity.ktQapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/GmailMessage.ktFapp/src/main/java/com/example/abonekaptanmobile/data/remote/GroqApi.ktAapp/src/main/java/com/example/abonekaptanmobile/ui/theme/Color.ktIapp/src/main/java/com/example/abonekaptanmobile/model/SubscriptionItem.ktLapp/src/main/java/com/example/abonekaptanmobile/ui/screens/FeedbackDialog.ktRapp/src/main/java/com/example/abonekaptanmobile/services/SubscriptionClassifier.ktIapp/src/main/java/com/example/abonekaptanmobile/auth/GoogleAuthManager.kt]app/src/main/java/com/example/abonekaptanmobile/data/repository/CommunityPatternRepository.ktIapp/src/main/java/com/example/abonekaptanmobile/data/local/AppDatabase.ktAapp/src/main/java/com/example/abonekaptanmobile/model/RawEmail.ktRapp/src/main/java/com/example/abonekaptanmobile/data/repository/EmailRepository.ktMapp/src/main/java/com/example/abonekaptanmobile/utils/HybridApproachTester.ktPapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/EmailEntity.ktMapp/src/main/java/com/example/abonekaptanmobile/ui/viewmodel/MainViewModel.ktTapp/src/main/java/com/example/abonekaptanmobile/ui/screens/SubscriptionListScreen.ktUapp/src/main/java/com/example/abonekaptanmobile/data/repository/FeedbackRepository.ktQapp/src/main/java/com/example/abonekaptanmobile/ui/screens/StageAnalysisScreen.ktKapp/src/main/java/com/example/abonekaptanmobile/ui/screens/LabTestDialog.ktQapp/src/main/java/com/example/abonekaptanmobile/data/repository/GroqRepository.kt?app/src/main/java/com/example/abonekaptanmobile/di/AppModule.ktRapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/StageAnalysisDao.ktAapp/src/main/java/com/example/abonekaptanmobile/ui/theme/Theme.ktHapp/src/main/java/com/example/abonekaptanmobile/model/ClassifiedEmail.ktJapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/EmailDao.ktSapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/FeedbackEntity.kt@app/src/main/java/com/example/abonekaptanmobile/ui/theme/Type.ktMapp/src/main/java/com/example/abonekaptanmobile/data/remote/HuggingFaceApi.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              